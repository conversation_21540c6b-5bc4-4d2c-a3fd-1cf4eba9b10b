# 自动驾驶大模型自闭环验证项目

本项目提供自动驾驶大模型的自闭环验证功能，支持两种执行模式以适应不同的使用场景。

## 🚀 项目特性

- **双执行模式**: 支持直接执行和RJob集群执行
- **模块化设计**: 核心业务逻辑与执行环境解耦
- **统一接口**: 提供一致的CLI和配置接口
- **灵活配置**: 针对不同执行模式的专门配置
- **完整流程**: 涵盖数据检查、替换、训练、评测和报告生成

## 🏗️ 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    统一CLI入口 (main.py)                    │
└─────────────────────┬───────────────────────────────────────┘
                      │
         ┌────────────┴────────────┐
         │                         │
    ┌────▼────┐               ┌────▼────┐
    │ Direct  │               │  RJob   │
    │ 直接执行  │               │ 集群执行  │
    └────┬────┘               └────┬────┘
         │                         │
    ┌────▼────────────────────────▼────┐
    │      TrainingPipeline           │
    │      (核心业务逻辑)               │
    └─────────────┬───────────────────┘
                  │
    ┌─────────────▼───────────────┐
    │        ExecutorBase         │
    │        (执行器基类)          │
    └─────┬───────────────┬───────┘
          │               │
    ┌─────▼─────┐   ┌─────▼─────┐
    │ Direct    │   │   RJob    │
    │ Executor  │   │ Executor  │
    └───────────┘   └───────────┘
```

## 📋 目录结构

```
AutoLoopValidation/
├── main.py                    # 统一CLI入口
├── scripts/
│   ├── direct_run.py         # 直接执行入口
│   ├── rjob_run.py          # RJob执行入口
│   └── submit_dry_run.py    # RJob干运行提交
├── src/
│   ├── core/                # 核心业务逻辑
│   │   └── pipeline.py      # 训练验证流程管理
│   ├── executors/           # 执行器实现
│   │   ├── base.py         # 执行器基类
│   │   ├── direct.py       # 直接执行器
│   │   └── rjob.py         # RJob执行器
│   ├── training/           # 训练相关模块
│   ├── data_checker/       # 数据检查模块
│   └── utils/              # 工具类
├── config/
│   ├── validation_config.yaml    # 通用配置
│   ├── direct_config.yaml       # 直接执行配置
│   └── rjob_config.yaml         # RJob执行配置
└── requirements.txt
```

## 🎯 执行模式对比

### 直接执行模式 (Direct)

**适用场景:**
- 本地开发和调试
- 小规模数据验证
- 快速原型验证
- 不需要集群资源的场景

**特点:**
- 在当前环境直接运行
- 资源需求较低
- 快速启动和迭代
- 适合开发阶段

### RJob执行模式 (RJob)

**适用场景:**
- 生产环境大规模训练
- 需要GPU集群资源的场景
- 长时间运行的训练任务
- 需要高性能计算资源的场景

**特点:**
- 通过RJob提交到集群
- 支持分布式训练
- 高性能计算资源
- 适合生产环境

## ⚡ 快速开始

### 1. 直接执行模式（推荐用于开发）

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 执行数据检查
python main.py direct -d /path/to/your/data --step check

# 3. 执行完整流程
python main.py direct -d /path/to/your/data --verbose
```

### 2. RJob执行模式（推荐用于生产）

```bash
# 1. 检查RJob环境
rjob version

# 2. 提交训练任务
python main.py rjob -d s3://your-bucket/data --step train

# 3. 查看任务状态
rjob status <job_name>
```

## 🛠️ 安装和配置

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd AutoLoopValidation

# 安装依赖
pip install -r requirements.txt

# 安装项目
pip install -e .
```

### 2. 配置文件

根据使用的执行模式，编辑相应的配置文件：

- **直接执行模式**: `config/direct_config.yaml`
- **RJob执行模式**: `config/rjob_config.yaml`

### 3. 环境变量（可选）

```bash
# AWS配置（用于S3访问）
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key

# RJob配置（如果使用RJob模式）
export RJOB_NAMESPACE=your_namespace
```

## 📖 使用指南

### 统一入口使用

```bash
# 查看帮助
python main.py --help

# 直接执行模式
python main.py direct -d /path/to/data

# RJob执行模式  
python main.py rjob -d s3://path/to/data

# 查看配置信息
python main.py config-info --mode direct
python main.py config-info --mode rjob
```

### 直接执行模式

```bash
# 使用统一入口
python main.py direct -d /path/to/data

# 或直接使用脚本
python scripts/direct_run.py -d /path/to/data

# 只执行特定步骤
python main.py direct -d /path/to/data --step check

# 干运行模式
python main.py direct -d /path/to/data --dry-run

# 详细输出
python main.py direct -d /path/to/data --verbose
```

### RJob执行模式

```bash
# 使用统一入口
python main.py rjob -d s3://path/to/data

# 或直接使用脚本
python scripts/rjob_run.py -d s3://path/to/data

# 只提交训练任务
python main.py rjob -d s3://path/to/data --step train

# 干运行模式（只生成命令）
python main.py rjob -d s3://path/to/data --dry-run

# 提交干运行容器
python scripts/submit_dry_run.py -c config/rjob_config.yaml
```

## 🔧 执行步骤说明

项目支持以下执行步骤：

- **all**: 执行完整流程（默认）
- **check**: 数据检查和验证
- **replace**: 数据替换
- **train**: 训练模型
- **eval**: 推理和评测
- **report**: 生成对比报告

### 示例：分步执行

```bash
# 1. 数据检查
python main.py direct -d /path/to/data --step check

# 2. 数据替换
python main.py direct -d /path/to/data --step replace

# 3. 训练
python main.py direct -d /path/to/data --step train

# 4. 评测
python main.py direct -d /path/to/data --step eval

# 5. 生成报告
python main.py direct -d /path/to/data --step report
```

## 📊 监控和日志

### 日志文件

- 直接执行模式: `direct_validation.log`
- RJob执行模式: `rjob_validation.log`
- 数据检查: `data_check.log`

### RJob任务监控

```bash
# 查看任务状态
rjob status <job_name>

# 查看任务日志
rjob logs <job_name>

# 进入容器
rjob exec <job_name> -- bash

# 删除任务
rjob delete <job_name>
```

## 🔍 故障排除

### 常见问题

1. **配置文件不存在**
   ```bash
   # 检查配置文件
   python main.py config-info --mode direct
   ```

2. **RJob命令不可用**
   ```bash
   # 检查RJob安装
   which rjob
   rjob version
   ```

3. **数据路径访问失败**
   ```bash
   # 检查S3访问权限
   aws s3 ls s3://your-bucket/
   ```

### 调试模式

```bash
# 启用详细输出
python main.py direct -d /path/to/data --verbose

# 使用干运行模式测试
python main.py direct -d /path/to/data --dry-run
```

## 🤝 开发指南

### 添加新的执行器

1. 在 `src/executors/` 目录下创建新的执行器类
2. 继承 `ExecutorBase` 基类
3. 实现所有抽象方法
4. 在 `__init__.py` 中注册新执行器

### 扩展配置

1. 在相应的配置文件中添加新配置项
2. 在执行器中读取和使用配置
3. 更新文档说明

## 📝 版本历史

- **v0.1.0**: 初始版本，支持双执行模式架构

## 📄 许可证

[添加许可证信息]

## 👥 贡献者

[添加贡献者信息]
