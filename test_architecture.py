#!/usr/bin/env python3
"""测试双执行模式架构

验证直接执行和RJob执行模式是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from src.core.pipeline import TrainingPipeline
        print("✓ TrainingPipeline 导入成功")
        
        from src.executors import ExecutorBase, DirectExecutor, RJobExecutor
        print("✓ 执行器模块导入成功")
        
        from src.utils.logger import setup_logger, get_logger
        print("✓ 日志模块导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False


def test_config_files():
    """测试配置文件"""
    print("\n测试配置文件...")
    
    config_files = [
        'config/validation_config.yaml',
        'config/direct_config.yaml', 
        'config/rjob_config.yaml'
    ]
    
    all_exist = True
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✓ {config_file} 存在")
        else:
            print(f"✗ {config_file} 不存在")
            all_exist = False
    
    return all_exist


def test_direct_executor():
    """测试直接执行器"""
    print("\n测试直接执行器...")
    
    try:
        from src.core.pipeline import TrainingPipeline
        
        # 使用默认配置创建直接执行器
        config_path = 'config/validation_config.yaml'
        if not os.path.exists(config_path):
            print(f"✗ 配置文件不存在: {config_path}")
            return False
        
        pipeline = TrainingPipeline(config_path, executor_type='direct')
        executor_info = pipeline.get_executor_info()
        
        print(f"✓ 直接执行器创建成功")
        print(f"  - 类型: {executor_info['type']}")
        print(f"  - 类名: {executor_info['class']}")
        
        return True
    except Exception as e:
        print(f"✗ 直接执行器测试失败: {e}")
        return False


def test_rjob_executor():
    """测试RJob执行器"""
    print("\n测试RJob执行器...")
    
    try:
        from src.core.pipeline import TrainingPipeline
        
        # 使用默认配置创建RJob执行器
        config_path = 'config/validation_config.yaml'
        if not os.path.exists(config_path):
            print(f"✗ 配置文件不存在: {config_path}")
            return False
        
        pipeline = TrainingPipeline(config_path, executor_type='rjob')
        executor_info = pipeline.get_executor_info()
        
        print(f"✓ RJob执行器创建成功")
        print(f"  - 类型: {executor_info['type']}")
        print(f"  - 类名: {executor_info['class']}")
        
        return True
    except Exception as e:
        print(f"✗ RJob执行器测试失败: {e}")
        return False


def test_cli_scripts():
    """测试CLI脚本"""
    print("\n测试CLI脚本...")
    
    scripts = [
        'main.py',
        'scripts/direct_run.py',
        'scripts/rjob_run.py',
        'scripts/submit_dry_run.py'
    ]
    
    all_exist = True
    for script in scripts:
        if os.path.exists(script):
            print(f"✓ {script} 存在")
        else:
            print(f"✗ {script} 不存在")
            all_exist = False
    
    return all_exist


def test_dry_run():
    """测试干运行模式"""
    print("\n测试干运行模式...")
    
    try:
        from src.core.pipeline import TrainingPipeline
        
        config_path = 'config/validation_config.yaml'
        if not os.path.exists(config_path):
            print(f"✗ 配置文件不存在: {config_path}")
            return False
        
        # 测试直接执行器的数据检查步骤
        pipeline = TrainingPipeline(config_path, executor_type='direct')
        
        # 模拟数据路径
        test_data_path = "/tmp/test_data"
        
        print("✓ 干运行模式测试准备完成")
        print("  注意: 实际的数据检查需要有效的数据路径")
        
        return True
    except Exception as e:
        print(f"✗ 干运行模式测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("自动驾驶大模型自闭环验证 - 架构测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("配置文件", test_config_files),
        ("直接执行器", test_direct_executor),
        ("RJob执行器", test_rjob_executor),
        ("CLI脚本", test_cli_scripts),
        ("干运行模式", test_dry_run)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！架构设置正确。")
        print("\n下一步:")
        print("1. 配置相应的数据路径")
        print("2. 运行实际的验证流程")
        print("3. 根据需要调整配置文件")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
    
    print("=" * 60)
    
    return passed == total


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
