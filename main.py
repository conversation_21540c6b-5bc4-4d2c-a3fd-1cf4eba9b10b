#!/usr/bin/env python3
"""自动驾驶大模型自闭环验证 - 统一入口

支持两种执行模式：
1. 直接执行模式 (direct) - 适用于本地开发、调试和小规模验证
2. RJob执行模式 (rjob) - 适用于生产环境、大规模训练和集群资源
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import click
from src.utils.logger import setup_logger, get_logger
from src.core.pipeline import TrainingPipeline

logger = get_logger(__name__)


@click.group()
@click.version_option(version="0.1.0", prog_name="AutoLoop Validation")
def cli():
    """自动驾驶大模型自闭环验证工具
    
    支持两种执行模式：
    
    \b
    1. 直接执行模式 (direct)
       - 适用于本地开发、调试和小规模验证
       - 在当前环境直接运行训练和评测
       - 资源需求较低，适合快速迭代
    
    \b
    2. R<PERSON>ob执行模式 (rjob)  
       - 适用于生产环境、大规模训练和集群资源
       - 通过RJob提交任务到GPU集群
       - 支持分布式训练和高性能计算
    
    使用示例：
    
    \b
    # 直接执行模式
    python main.py direct -d /path/to/data
    
    \b
    # RJob执行模式
    python main.py rjob -d s3://path/to/data
    
    \b
    # 查看帮助
    python main.py direct --help
    python main.py rjob --help
    """
    pass


@cli.command()
@click.option('--config', '-c', 
              default='config/direct_config.yaml',
              help='配置文件路径')
@click.option('--data-path', '-d', required=True, 
              help='新数据集路径')
@click.option('--dry-run', is_flag=True, 
              help='只生成命令不执行')
@click.option('--step', 
              type=click.Choice(['all', 'check', 'replace', 'train', 'eval', 'report']), 
              default='all', 
              help='执行步骤')
@click.option('--log-file', 
              default='direct_validation.log',
              help='日志文件路径')
@click.option('--verbose', '-v', is_flag=True,
              help='详细输出模式')
def direct(config: str, data_path: str, dry_run: bool, step: str, log_file: str, verbose: bool):
    """直接执行模式
    
    在本地或当前环境中直接执行训练验证流程。
    
    适用场景：
    - 本地开发和调试
    - 小规模数据验证
    - 快速原型验证
    - 不需要集群资源的场景
    
    示例：
    
    \b
    # 执行完整流程
    python main.py direct -d /path/to/data
    
    \b
    # 只执行数据检查
    python main.py direct -d /path/to/data --step check
    
    \b
    # 干运行模式
    python main.py direct -d /path/to/data --dry-run
    """
    _run_pipeline('direct', config, data_path, dry_run, step, log_file, verbose)


@cli.command()
@click.option('--config', '-c', 
              default='config/rjob_config.yaml',
              help='配置文件路径')
@click.option('--data-path', '-d', required=True, 
              help='新数据集路径')
@click.option('--dry-run', is_flag=True, 
              help='只生成RJob命令不执行')
@click.option('--step', 
              type=click.Choice(['all', 'check', 'replace', 'train', 'eval', 'report']), 
              default='all', 
              help='执行步骤')
@click.option('--log-file', 
              default='rjob_validation.log',
              help='日志文件路径')
@click.option('--verbose', '-v', is_flag=True,
              help='详细输出模式')
def rjob(config: str, data_path: str, dry_run: bool, step: str, log_file: str, verbose: bool):
    """RJob执行模式
    
    通过RJob集群执行训练验证流程。
    
    适用场景：
    - 生产环境大规模训练
    - 需要GPU集群资源的场景
    - 长时间运行的训练任务
    - 需要高性能计算资源的场景
    
    示例：
    
    \b
    # 执行完整流程
    python main.py rjob -d s3://path/to/data
    
    \b
    # 只提交训练任务
    python main.py rjob -d s3://path/to/data --step train
    
    \b
    # 干运行模式（只生成RJob命令）
    python main.py rjob -d s3://path/to/data --dry-run
    """
    _run_pipeline('rjob', config, data_path, dry_run, step, log_file, verbose)


@cli.command()
@click.option('--mode', 
              type=click.Choice(['direct', 'rjob']), 
              default='direct',
              help='执行模式')
def config_info(mode: str):
    """显示配置信息"""
    
    config_files = {
        'direct': 'config/direct_config.yaml',
        'rjob': 'config/rjob_config.yaml'
    }
    
    config_path = config_files[mode]
    
    click.echo(f"执行模式: {mode}")
    click.echo(f"默认配置文件: {config_path}")
    
    if os.path.exists(config_path):
        click.echo("✓ 配置文件存在")
        
        try:
            pipeline = TrainingPipeline(config_path, mode)
            config = pipeline.get_config()
            
            click.echo(f"\n项目信息:")
            click.echo(f"  名称: {config.get('project', {}).get('name', 'N/A')}")
            click.echo(f"  版本: {config.get('project', {}).get('version', 'N/A')}")
            
            if mode == 'rjob':
                rjob_config = config.get('rjob', {})
                click.echo(f"\nRJob配置:")
                click.echo(f"  镜像: {rjob_config.get('image', 'N/A')}")
                click.echo(f"  命名空间: {rjob_config.get('namespace', 'N/A')}")
                click.echo(f"  CPU: {rjob_config.get('cpu', 'N/A')}")
                click.echo(f"  GPU: {rjob_config.get('gpu', 'N/A')}")
                click.echo(f"  内存: {rjob_config.get('memory', 'N/A')}")
            
        except Exception as e:
            click.echo(f"✗ 配置文件解析失败: {e}", err=True)
    else:
        click.echo("✗ 配置文件不存在")


def _run_pipeline(executor_type: str, config: str, data_path: str, 
                 dry_run: bool, step: str, log_file: str, verbose: bool):
    """运行训练验证流程的通用函数"""
    
    # 设置日志
    setup_logger(log_file=log_file, verbose=verbose)
    logger.info("=" * 60)
    logger.info(f"开始{executor_type.upper()}执行自动驾驶大模型自闭环验证")
    logger.info("=" * 60)
    
    # 检查配置文件是否存在
    if not os.path.exists(config):
        logger.error(f"配置文件不存在: {config}")
        click.echo(f"错误: 配置文件不存在: {config}", err=True)
        sys.exit(1)
    
    try:
        # 创建训练流程管理器
        pipeline = TrainingPipeline(config_path=config, executor_type=executor_type)
        
        # 显示执行信息
        executor_info = pipeline.get_executor_info()
        logger.info(f"执行器类型: {executor_info['type']}")
        logger.info(f"执行器类: {executor_info['class']}")
        logger.info(f"配置文件: {executor_info['config_path']}")
        logger.info(f"数据路径: {data_path}")
        logger.info(f"执行步骤: {step}")
        logger.info(f"干运行模式: {dry_run}")
        
        if verbose:
            click.echo(f"✓ 使用配置文件: {config}")
            click.echo(f"✓ 数据路径: {data_path}")
            click.echo(f"✓ 执行步骤: {step}")
            click.echo(f"✓ 执行器: {executor_type.upper()}模式")
            if dry_run:
                if executor_type == 'rjob':
                    click.echo("⚠ 干运行模式 - 只生成RJob命令不实际提交")
                else:
                    click.echo("⚠ 干运行模式 - 不会实际执行训练")
        
        # 执行流程
        result = pipeline.run(
            data_path=data_path,
            dry_run=dry_run,
            step=step
        )
        
        # 显示结果
        if result['success']:
            logger.info("✓ 验证流程执行成功")
            if verbose:
                click.echo("\n" + "=" * 40)
                click.echo("执行结果:")
                click.echo(f"✓ 成功完成步骤: {', '.join(result['steps_completed'])}")
                
                # 显示特定执行器的信息
                if executor_type == 'rjob' and 'training' in result and 'job_name' in result['training']:
                    job_name = result['training']['job_name']
                    click.echo(f"✓ RJob任务名称: {job_name}")
                    if not dry_run:
                        click.echo(f"✓ 可以使用以下命令查看任务状态:")
                        click.echo(f"  rjob status {job_name}")
                        click.echo(f"✓ 可以使用以下命令进入容器:")
                        click.echo(f"  rjob exec {job_name} -- bash")
        else:
            logger.error("✗ 验证流程执行失败")
            if result.get('errors'):
                for error in result['errors']:
                    logger.error(f"错误: {error}")
                    if verbose:
                        click.echo(f"✗ 错误: {error}", err=True)
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("用户中断执行")
        click.echo("\n用户中断执行")
        sys.exit(130)
    except Exception as e:
        logger.error(f"执行失败: {e}")
        click.echo(f"错误: {e}", err=True)
        if verbose:
            import traceback
            logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == '__main__':
    cli()
