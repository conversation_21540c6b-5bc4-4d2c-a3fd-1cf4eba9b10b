"""日志工具模块"""

import os
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(
    log_file: Optional[str] = None,
    log_level: str = "INFO",
    log_dir: str = "outputs/logs"
) -> None:
    """设置日志配置
    
    Args:
        log_file: 日志文件名
        log_level: 日志级别
        log_dir: 日志目录
    """
    # 创建日志目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # 移除默认处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sink=lambda msg: print(msg, end=""),
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 添加文件输出
    if log_file:
        log_path = os.path.join(log_dir, log_file)
        logger.add(
            sink=log_path,
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="100 MB",
            retention="30 days"
        )


def get_logger(name: str) -> logger:
    """获取指定名称的日志器"""
    return logger.bind(name=name)