"""训练验证流程核心逻辑

包含训练验证流程的核心业务逻辑，与具体执行环境解耦
"""

import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from src.utils.logger import get_logger
from src.executors import ExecutorBase, DirectExecutor, RJobExecutor

logger = get_logger(__name__)


class TrainingPipeline:
    """训练验证流程管理器
    
    负责管理整个训练验证流程的执行，支持不同的执行器
    """
    
    def __init__(self, config_path: str, executor_type: str = 'direct'):
        """初始化流程管理器
        
        Args:
            config_path: 配置文件路径
            executor_type: 执行器类型 ('direct' 或 'rjob')
        """
        self.config_path = config_path
        self.executor_type = executor_type
        self.config = self._load_config()
        self.executor = self._create_executor()
        
        logger.info(f"初始化训练验证流程 - 执行器类型: {executor_type}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"加载配置文件: {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def _create_executor(self) -> ExecutorBase:
        """创建执行器实例"""
        if self.executor_type == 'direct':
            return DirectExecutor(self.config)
        elif self.executor_type == 'rjob':
            return RJobExecutor(self.config)
        else:
            raise ValueError(f"不支持的执行器类型: {self.executor_type}")
    
    def run(self, 
            data_path: str, 
            dry_run: bool = False,
            step: str = 'all') -> Dict[str, Any]:
        """运行训练验证流程
        
        Args:
            data_path: 新数据集路径
            dry_run: 是否为干运行模式
            step: 执行步骤 ('all', 'check', 'replace', 'train', 'eval', 'report')
            
        Returns:
            执行结果字典
        """
        logger.info("开始自动驾驶大模型自闭环验证")
        logger.info(f"数据路径: {data_path}")
        logger.info(f"执行器类型: {self.executor_type}")
        logger.info(f"执行步骤: {step}")
        logger.info(f"干运行模式: {dry_run}")
        
        try:
            result = self.executor.execute_full_pipeline(
                data_path=data_path,
                dry_run=dry_run,
                step=step
            )
            
            logger.info("训练验证流程完成")
            return result
            
        except Exception as e:
            logger.error(f"训练验证流程失败: {e}")
            raise
    
    def run_step(self, 
                 step: str, 
                 data_path: str = None, 
                 dry_run: bool = False) -> Dict[str, Any]:
        """运行单个步骤
        
        Args:
            step: 步骤名称 ('check', 'replace', 'train', 'eval', 'report')
            data_path: 数据路径 (某些步骤需要)
            dry_run: 是否为干运行模式
            
        Returns:
            执行结果字典
        """
        logger.info(f"执行单个步骤: {step}")
        
        try:
            if step == 'check':
                if not data_path:
                    raise ValueError("数据检查步骤需要提供data_path参数")
                return self.executor.execute_data_check(data_path)
            
            elif step == 'replace':
                if not data_path:
                    raise ValueError("数据替换步骤需要提供data_path参数")
                return self.executor.execute_data_replacement(data_path)
            
            elif step == 'train':
                return self.executor.execute_training(dry_run)
            
            elif step == 'eval':
                return self.executor.execute_evaluation()
            
            elif step == 'report':
                return self.executor.execute_report_generation()
            
            else:
                raise ValueError(f"不支持的步骤: {step}")
                
        except Exception as e:
            logger.error(f"步骤 {step} 执行失败: {e}")
            raise
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.config.copy()
    
    def get_executor_info(self) -> Dict[str, Any]:
        """获取执行器信息"""
        return {
            'type': self.executor_type,
            'class': self.executor.__class__.__name__,
            'config_path': self.config_path
        }
    
    @staticmethod
    def create_from_config_dir(config_dir: str, 
                              executor_type: str = 'direct') -> 'TrainingPipeline':
        """从配置目录创建流程管理器
        
        Args:
            config_dir: 配置目录路径
            executor_type: 执行器类型
            
        Returns:
            TrainingPipeline实例
        """
        config_dir = Path(config_dir)
        
        # 根据执行器类型选择配置文件
        if executor_type == 'direct':
            config_files = ['direct_config.yaml', 'validation_config.yaml']
        elif executor_type == 'rjob':
            config_files = ['rjob_config.yaml', 'validation_config.yaml']
        else:
            config_files = ['validation_config.yaml']
        
        # 查找存在的配置文件
        config_path = None
        for config_file in config_files:
            potential_path = config_dir / config_file
            if potential_path.exists():
                config_path = str(potential_path)
                break
        
        if not config_path:
            # 使用默认配置文件
            config_path = str(config_dir / 'validation_config.yaml')
        
        return TrainingPipeline(config_path, executor_type)
