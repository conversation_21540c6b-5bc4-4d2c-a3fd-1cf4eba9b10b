"""RJob训练任务提交器"""

import subprocess
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from src.utils.logger import get_logger

logger = get_logger(__name__)


class RJobSubmitter:
    """RJob训练任务提交器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.rjob_config = config.get("rjob", {})
    
    def generate_job_name(self, prefix: str = "validation") -> str:
        """生成任务名称"""
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        return f"{prefix}-{timestamp}"
    
    def build_rjob_command(self, 
                          job_name: Optional[str] = None,
                          custom_args: Optional[Dict[str, Any]] = None) -> List[str]:
        """构建RJob提交命令
        
        Args:
            job_name: 任务名称
            custom_args: 自定义参数
            
        Returns:
            命令列表
        """
        if not job_name:
            job_name = self.generate_job_name()
        
        # 基础命令
        cmd = [
            "rjob", "submit",
            "--image-pull-policy=always",
            "--replica=1",
            f"--name={job_name}",
            "--restart-policy=never",
            "--preemptible=no",
            f"--cpu={self.rjob_config.get('cpu', 32)}",
            f"--gpu={self.rjob_config.get('gpu', 2)}",
            f"--memory={self.rjob_config.get('memory', 200000)}",
            "--folder", ".",
            f"--namespace={self.rjob_config.get('namespace', 'mach-generator')}",
            f"--positive-tags={self.rjob_config.get('positive_tags', 'H100')}",
            f"--charged-group={self.rjob_config.get('charged_group', 'gpu_gernerator')}",
            "--private-machine=group",
        ]
        
        # 环境变量
        env_vars = [
            "DISTRIBUTED_JOB=true",
            "NCCL_IB_TIMEOUT=7200000",
            "CUDA_DEVICE_MAX_CONNECTIONS=1",
            "NCCL_GID_INDEX=3",
            "NCCL_ALGO=RING",
            "TORCH_DDP_GRAD_AS_BUCKET_VIEW=1",
            "TORCH_DDP_BUCKET_CAP_MB=64",
            "no_proxy=i.brainpp.cn,mach-drive-inc.com,ivolces.com,clear.ml,kubebrain,kubebrain.com,svc,kubebrain.local,machdrive.cn,megvii-inc.com,127.0.0.1,10.0.0.0/8,localhost,svc",
            "http_proxy=http://proxy.machdrive.cn:3128",
            "https_proxy=http://proxy.machdrive.cn:3128",
            "all_proxy=http://proxy.machdrive.cn:3128",
            f"DET3D_EXPID={uuid.uuid4()}"
        ]
        
        for env_var in env_vars:
            cmd.extend(["-e", env_var])
        
        # 其他配置
        cmd.extend([
            "--custom-resources=rdma/mlnx_shared=8",
            "--custom-resources=mellanox.com/mlnx_rdma=8",
            "--gang-start=false",
            f"--image={self.rjob_config.get('image')}",
            "--mount=juicefs+s3://oss.i.machdrive.cn/jitao-juicefs:/data/storage",
            "--mount=gpfs://gpfs1/acceldata:/mnt/acceldata",
            "--",
            "zsh", "-c", "tail -f /dev/null"
        ])
        
        return cmd
    
    def submit_training_job(self, 
                           job_name: Optional[str] = None,
                           dry_run: bool = False) -> Dict[str, Any]:
        """提交训练任务
        
        Args:
            job_name: 任务名称
            dry_run: 是否只生成命令不执行
            
        Returns:
            提交结果
        """
        result = {
            "success": False,
            "job_name": job_name or self.generate_job_name(),
            "command": [],
            "output": "",
            "error": ""
        }
        
        try:
            cmd = self.build_rjob_command(result["job_name"])
            result["command"] = cmd
            
            logger.info(f"准备提交训练任务: {result['job_name']}")
            logger.info(f"命令: {' '.join(cmd)}")
            
            if dry_run:
                logger.info("Dry run模式，不实际执行命令")
                result["success"] = True
                return result
            
            # 执行命令
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            result["output"] = process.stdout
            result["error"] = process.stderr
            result["success"] = process.returncode == 0
            
            if result["success"]:
                logger.info(f"训练任务提交成功: {result['job_name']}")
            else:
                logger.error(f"训练任务提交失败: {result['error']}")
                
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"提交训练任务时出错: {e}")
        
        return result

    def submit_dry_run_job(self, job_name: Optional[str] = None) -> Dict[str, Any]:
        """提交一个dry-run任务，执行tail -f /dev/null保持容器运行
        
        Args:
            job_name: 任务名称
            
        Returns:
            提交结果
        """
        if not job_name:
            job_name = self.generate_job_name("dry-run")
        
        result = {
            "success": False,
            "job_name": job_name,
            "command": [],
            "output": "",
            "error": ""
        }
        
        try:
            cmd = self.build_rjob_command(job_name)
            result["command"] = cmd
            
            logger.info(f"准备提交dry-run任务: {job_name}")
            logger.info(f"命令: {' '.join(cmd)}")
            
            # 执行命令
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            result["output"] = process.stdout
            result["error"] = process.stderr
            result["success"] = process.returncode == 0
            
            if result["success"]:
                logger.info(f"Dry-run任务提交成功: {job_name}")
                logger.info("任务将保持运行状态，可以通过rjob exec进入容器")
            else:
                logger.error(f"Dry-run任务提交失败: {result['error']}")
                
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"提交dry-run任务时出错: {e}")
        
        return result
