"""S3数据路径检查器"""

import boto3
from typing import Dict, Any, Optional
from urllib.parse import urlparse
from src.utils.logger import get_logger

logger = get_logger(__name__)


class S3Checker:
    """S3路径和文件检查器"""
    
    def __init__(self, aws_access_key: Optional[str] = None, aws_secret_key: Optional[str] = None):
        """初始化S3客户端"""
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key
        ) if aws_access_key else boto3.client('s3')
    
    def check_s3_path(self, s3_path: str) -> Dict[str, Any]:
        """检查S3路径是否存在且可访问
        
        Args:
            s3_path: S3路径，格式如 s3://bucket/path/to/file
            
        Returns:
            检查结果字典
        """
        result = {
            "path": s3_path,
            "exists": False,
            "accessible": False,
            "size": 0,
            "error": None
        }
        
        try:
            # 解析S3路径
            parsed = urlparse(s3_path)
            bucket = parsed.netloc
            key = parsed.path.lstrip('/')
            
            logger.info(f"检查S3路径: {s3_path}")
            logger.info(f"Bucket: {bucket}, Key: {key}")
            
            # 检查文件是否存在
            response = self.s3_client.head_object(Bucket=bucket, Key=key)
            
            result["exists"] = True
            result["accessible"] = True
            result["size"] = response.get("ContentLength", 0)
            result["last_modified"] = response.get("LastModified")
            
            logger.info(f"文件存在，大小: {result['size']} bytes")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"检查S3路径失败: {e}")
        
        return result
    
    def list_s3_directory(self, s3_path: str, max_keys: int = 1000) -> Dict[str, Any]:
        """列出S3目录下的文件
        
        Args:
            s3_path: S3目录路径
            max_keys: 最大返回文件数
            
        Returns:
            目录内容字典
        """
        result = {
            "path": s3_path,
            "files": [],
            "total_size": 0,
            "file_count": 0,
            "error": None
        }
        
        try:
            parsed = urlparse(s3_path)
            bucket = parsed.netloc
            prefix = parsed.path.lstrip('/')
            
            response = self.s3_client.list_objects_v2(
                Bucket=bucket,
                Prefix=prefix,
                MaxKeys=max_keys
            )
            
            if 'Contents' in response:
                for obj in response['Contents']:
                    file_info = {
                        "key": obj['Key'],
                        "size": obj['Size'],
                        "last_modified": obj['LastModified']
                    }
                    result["files"].append(file_info)
                    result["total_size"] += obj['Size']
                
                result["file_count"] = len(result["files"])
                logger.info(f"找到 {result['file_count']} 个文件，总大小: {result['total_size']} bytes")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"列出S3目录失败: {e}")
        
        return result