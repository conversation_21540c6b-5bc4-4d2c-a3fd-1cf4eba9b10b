"""执行器基类

定义训练验证流程执行器的统一接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ExecutorBase(ABC):
    """执行器基类
    
    定义训练验证流程的统一执行接口，不同的执行器实现可以在不同环境中运行
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化执行器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logger
    
    @abstractmethod
    def execute_data_check(self, data_path: str) -> Dict[str, Any]:
        """执行数据检查
        
        Args:
            data_path: 数据路径
            
        Returns:
            检查结果字典
        """
        pass
    
    @abstractmethod
    def execute_data_replacement(self, new_data_path: str) -> Dict[str, Any]:
        """执行数据替换
        
        Args:
            new_data_path: 新数据路径
            
        Returns:
            替换结果字典
        """
        pass
    
    @abstractmethod
    def execute_training(self, dry_run: bool = False) -> Dict[str, Any]:
        """执行训练
        
        Args:
            dry_run: 是否为干运行模式
            
        Returns:
            训练结果字典
        """
        pass
    
    @abstractmethod
    def execute_evaluation(self) -> Dict[str, Any]:
        """执行评测
        
        Returns:
            评测结果字典
        """
        pass
    
    @abstractmethod
    def execute_report_generation(self) -> Dict[str, Any]:
        """执行报告生成
        
        Returns:
            报告生成结果字典
        """
        pass
    
    def execute_full_pipeline(self, 
                             data_path: str, 
                             dry_run: bool = False,
                             step: str = 'all') -> Dict[str, Any]:
        """执行完整的验证流程
        
        Args:
            data_path: 数据路径
            dry_run: 是否为干运行模式
            step: 执行步骤 ('all', 'check', 'replace', 'train', 'eval', 'report')
            
        Returns:
            执行结果字典
        """
        results = {
            'success': True,
            'steps_completed': [],
            'errors': []
        }
        
        try:
            if step in ['all', 'check']:
                self.logger.info("=== 步骤1: 检查新数据集格式 ===")
                check_result = self.execute_data_check(data_path)
                results['data_check'] = check_result
                results['steps_completed'].append('check')
            
            if step in ['all', 'replace']:
                self.logger.info("=== 步骤2: 替换训练数据 ===")
                replace_result = self.execute_data_replacement(data_path)
                results['data_replacement'] = replace_result
                results['steps_completed'].append('replace')
            
            if step in ['all', 'train']:
                self.logger.info("=== 步骤3: 执行训练 ===")
                train_result = self.execute_training(dry_run)
                results['training'] = train_result
                results['steps_completed'].append('train')
            
            if step in ['all', 'eval']:
                self.logger.info("=== 步骤4-6: 推理、评测和可视化 ===")
                eval_result = self.execute_evaluation()
                results['evaluation'] = eval_result
                results['steps_completed'].append('eval')
            
            if step in ['all', 'report']:
                self.logger.info("=== 步骤7: 生成对比报告 ===")
                report_result = self.execute_report_generation()
                results['report'] = report_result
                results['steps_completed'].append('report')
            
            self.logger.info("验证流程完成")
            
        except Exception as e:
            results['success'] = False
            results['errors'].append(str(e))
            self.logger.error(f"验证流程失败: {e}")
            raise
        
        return results
    
    @property
    def executor_type(self) -> str:
        """获取执行器类型"""
        return self.__class__.__name__
