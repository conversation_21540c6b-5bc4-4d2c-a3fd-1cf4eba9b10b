"""直接执行器

在本地或当前环境中直接执行训练验证流程
"""

import subprocess
import os
from typing import Dict, Any
from .base import ExecutorBase
from src.data_checker import S3<PERSON><PERSON><PERSON>, FormatValidator


class DirectExecutor(ExecutorBase):
    """直接执行器
    
    在本地或当前环境中直接执行训练验证流程，适用于：
    - 本地开发和调试
    - 小规模数据验证
    - 快速原型验证
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化直接执行器"""
        super().__init__(config)
        self.logger.info("初始化直接执行器")
    
    def execute_data_check(self, data_path: str) -> Dict[str, Any]:
        """执行数据检查"""
        self.logger.info(f"直接执行数据检查: {data_path}")
        
        result = {
            'success': False,
            'data_path': data_path,
            'checks': {}
        }
        
        try:
            # S3路径检查
            s3_checker = S3Checker()
            s3_result = s3_checker.check_s3_path(data_path)
            result['checks']['s3_check'] = s3_result
            
            if not s3_result["exists"]:
                raise ValueError(f"数据路径不存在: {data_path}")
            
            # 格式验证 (如果是本地文件)
            if not data_path.startswith('s3://'):
                validator = FormatValidator()
                format_result = validator.validate_json_format(data_path)
                result['checks']['format_check'] = format_result
                
                if not format_result["is_valid"]:
                    raise ValueError(f"数据格式验证失败: {format_result['errors']}")
            
            result['success'] = True
            self.logger.info("数据检查通过")
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"数据检查失败: {e}")
            raise
        
        return result
    
    def execute_data_replacement(self, new_data_path: str) -> Dict[str, Any]:
        """执行数据替换"""
        self.logger.info(f"直接执行数据替换: {new_data_path}")
        
        result = {
            'success': False,
            'new_data_path': new_data_path,
            'replacement_ratio': self.config["data"]["replacement_ratio"]
        }
        
        try:
            # TODO: 实现直接数据替换逻辑
            # 这里可以直接调用数据处理函数，而不是通过RJob
            replacement_ratio = self.config["data"]["replacement_ratio"]
            self.logger.info(f"准备替换 {replacement_ratio*100}% 的训练数据")
            
            # 模拟数据替换过程
            self.logger.info("执行本地数据替换...")
            result['success'] = True
            result['message'] = "数据替换功能待实现 (直接执行模式)"
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"数据替换失败: {e}")
            raise
        
        return result
    
    def execute_training(self, dry_run: bool = False) -> Dict[str, Any]:
        """执行训练"""
        self.logger.info("直接执行训练")
        
        result = {
            'success': False,
            'dry_run': dry_run,
            'training_config': self.config.get('training', {})
        }
        
        try:
            if dry_run:
                self.logger.info("Dry run模式，不实际执行训练")
                result['success'] = True
                result['message'] = "Dry run - 训练命令已生成但未执行"
                return result
            
            # 构建训练命令
            training_config = self.config.get('training', {})
            exp_file = training_config.get('exp')
            ckpt_path = training_config.get('ckpt')
            
            if not exp_file:
                raise ValueError("训练配置中缺少exp文件路径")
            
            # 构建训练命令 (示例)
            cmd = [
                'python', 'train.py',  # 假设有一个train.py脚本
                '--config', exp_file,
                '--checkpoint', ckpt_path,
                '--max-epoch', str(training_config.get('max_epoch', 4)),
                '--batch-size', str(training_config.get('batch_size_per_device', 10))
            ]
            
            result['command'] = cmd
            self.logger.info(f"执行训练命令: {' '.join(cmd)}")
            
            # 执行训练 (这里需要根据实际训练脚本调整)
            # process = subprocess.run(cmd, capture_output=True, text=True)
            # result['output'] = process.stdout
            # result['error'] = process.stderr
            # result['success'] = process.returncode == 0
            
            # 暂时模拟训练完成
            result['success'] = True
            result['message'] = "直接训练功能待实现 - 需要配置实际的训练脚本"
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"训练执行失败: {e}")
            raise
        
        return result
    
    def execute_evaluation(self) -> Dict[str, Any]:
        """执行评测"""
        self.logger.info("直接执行评测")
        
        result = {
            'success': False,
            'evaluation_config': self.config.get('evaluation', {})
        }
        
        try:
            # TODO: 实现直接评测逻辑
            self.logger.info("执行本地推理和评测...")
            result['success'] = True
            result['message'] = "推理和评测功能待实现 (直接执行模式)"
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"评测执行失败: {e}")
            raise
        
        return result
    
    def execute_report_generation(self) -> Dict[str, Any]:
        """执行报告生成"""
        self.logger.info("直接执行报告生成")
        
        result = {
            'success': False,
            'output_config': self.config.get('output', {})
        }
        
        try:
            # TODO: 实现直接报告生成逻辑
            output_dir = self.config.get('output', {}).get('reports_dir', 'outputs/reports')
            os.makedirs(output_dir, exist_ok=True)
            
            self.logger.info(f"生成报告到: {output_dir}")
            result['success'] = True
            result['output_dir'] = output_dir
            result['message'] = "报告生成功能待实现 (直接执行模式)"
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"报告生成失败: {e}")
            raise
        
        return result
