# 验证配置文件
project:
  name: "autoloop_validation"
  version: "0.1.0"

data:
  replacement_ratio: 0.08  # 替换比例
  training_repo: "https://git-core.megvii-inc.com/transformer/Perceptron/-/tree/auto_loop_validation"
  train_data: "whole-with-vis-attrs"  # 自闭换验证目标数据集
  train_data_list: 
    - "s3://liuzhengmao/tmp0523/jsons/all_filtered.json"
  val_data: "whole-pth-bmk"  # 评测数据集
  val_data_list:
    - "s3://liuzhengmao/tmp0523/prelabel/bmk_filtered/0523_e2e_list.json"
  data_mode: "qyjpg"

rjob:
  image: "registry.qy.machdrive.cn/mach-generator/perceptron:ld-0.1"
  namespace: "mach-generator"
  charged_group: "generator_gpu"
  cpu: 32
  gpu: 2
  memory: 200000
  positive_tags: "H100"
  
training:
  exp: "perceptron/exps/end2end/private/maptrv2/0701_bsl_exp/maptrv2_exp_4v0r_con-dis_merge_br_wpre_z10_p177_fasternet_nowarp_pre_la_v2.py" # z10 模型
  #exp: "perceptron/exps/end2end/private/maptrv2/0701_bsl_exp/maptrv2_exp_4v0r_con-dis_merge_br_wpre_p177_repvggb1_warp_ft_la_renori.py" # p177 模型
  ckpt: "s3://cjz-share/con-dis_model/ckpt_release/0726/z10_con-dis_model_0726_v0.pth"
  max_epoch: 4
  batch_size_per_device: 10
  total_devices: 1

inference:
  is_hdmap: false # 是否使用高精地图

evaluation:
  baseline_model_path: ""  # 待配置
  metrics:
    - "accuracy"
    - "precision" 
    - "recall"
    - "f1_score"

output:
  logs_dir: "outputs/logs"
  reports_dir: "outputs/reports"
  visualizations_dir: "outputs/visualizations"