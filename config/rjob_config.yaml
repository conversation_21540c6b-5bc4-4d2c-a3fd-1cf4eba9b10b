# RJob执行模式配置文件
# 适用于生产环境、大规模训练和集群资源

project:
  name: "autoloop_validation"
  version: "0.1.0"
  execution_mode: "rjob"

data:
  replacement_ratio: 0.08  # 替换比例
  training_repo: "https://git-core.megvii-inc.com/transformer/Perceptron/-/tree/auto_loop_validation"
  train_data: "whole-with-vis-attrs"  # 自闭环验证目标数据集
  train_data_list: 
    - "s3://liuzhengmao/tmp0523/jsons/all_filtered.json"
  val_data: "whole-pth-bmk"  # 评测数据集
  val_data_list:
    - "s3://liuzhengmao/tmp0523/prelabel/bmk_filtered/0523_e2e_list.json"
  data_mode: "qyjpg"

# RJob集群配置
rjob:
  image: "registry.qy.machdrive.cn/mach-generator/perceptron:ld-0.1"
  namespace: "mach-generator"
  charged_group: "generator_gpu"
  cpu: 32
  gpu: 2
  memory: 200000
  positive_tags: "H100"
  
  # 高级RJob配置
  advanced:
    preemptible: false
    restart_policy: "never"
    image_pull_policy: "always"
    gang_start: false
    timeout_hours: 24
    
  # 环境变量
  environment:
    DISTRIBUTED_JOB: "true"
    NCCL_IB_TIMEOUT: "7200000"
    CUDA_DEVICE_MAX_CONNECTIONS: "1"
    NCCL_GID_INDEX: "3"
    NCCL_ALGO: "RING"
    TORCH_DDP_GRAD_AS_BUCKET_VIEW: "1"
    TORCH_DDP_BUCKET_CAP_MB: "64"
    
  # 挂载配置
  mounts:
    - "juicefs+s3://oss.i.machdrive.cn/jitao-juicefs:/data/storage"
    - "gpfs://gpfs1/acceldata:/mnt/acceldata"
    
  # 自定义资源
  custom_resources:
    - "rdma/mlnx_shared=8"
    - "mellanox.com/mlnx_rdma=8"

# 生产环境训练配置
training:
  exp: "perceptron/exps/end2end/private/maptrv2/0701_bsl_exp/maptrv2_exp_4v0r_con-dis_merge_br_wpre_z10_p177_fasternet_nowarp_pre_la_v2.py"
  ckpt: "s3://cjz-share/con-dis_model/ckpt_release/0726/z10_con-dis_model_0726_v0.pth"
  max_epoch: 4
  batch_size_per_device: 10
  total_devices: 2  # 使用多GPU
  
  # 分布式训练配置
  distributed:
    backend: "nccl"
    init_method: "env://"
    world_size: 2
    
  # 训练任务配置
  job:
    priority: "high"
    queue: "gpu"
    max_retry: 3

inference:
  is_hdmap: false # 是否使用高精地图
  
  # 集群推理配置
  cluster:
    batch_size: 16
    num_workers: 8
    distributed: true

evaluation:
  baseline_model_path: ""  # 待配置
  metrics:
    - "accuracy"
    - "precision" 
    - "recall"
    - "f1_score"
  
  # 集群评测配置
  cluster:
    parallel_jobs: 8
    timeout_seconds: 7200
    use_gpu: true

output:
  logs_dir: "s3://validation-outputs/logs"
  reports_dir: "s3://validation-outputs/reports"
  visualizations_dir: "s3://validation-outputs/visualizations"
  
  # 集群输出配置
  cluster:
    save_intermediate: true
    compress_outputs: true
    backup_to_s3: true

# 监控和通知配置
monitoring:
  enabled: true
  metrics_endpoint: "http://prometheus.internal:9090"
  alert_webhook: "https://hooks.slack.com/services/..."
  
  # 监控指标
  metrics:
    - "gpu_utilization"
    - "memory_usage"
    - "training_loss"
    - "validation_accuracy"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_rotation: "1 day"
  max_files: 30
  
  # 集群日志配置
  cluster:
    centralized: true
    log_server: "elasticsearch.internal:9200"
    index_pattern: "validation-logs-*"
