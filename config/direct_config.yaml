# 直接执行模式配置文件
# 适用于本地开发、调试和小规模验证

project:
  name: "autoloop_validation"
  version: "0.1.0"
  execution_mode: "direct"

data:
  replacement_ratio: 0.08  # 替换比例
  training_repo: "https://git-core.megvii-inc.com/transformer/Perceptron/-/tree/auto_loop_validation"
  train_data: "whole-with-vis-attrs"  # 自闭环验证目标数据集
  train_data_list: 
    - "s3://liuzhengmao/tmp0523/jsons/all_filtered.json"
  val_data: "whole-pth-bmk"  # 评测数据集
  val_data_list:
    - "s3://liuzhengmao/tmp0523/prelabel/bmk_filtered/0523_e2e_list.json"
  data_mode: "qyjpg"

# 直接执行模式的训练配置
training:
  # 本地训练脚本路径
  script_path: "train.py"
  
  # 实验配置文件
  exp: "perceptron/exps/end2end/private/maptrv2/0701_bsl_exp/maptrv2_exp_4v0r_con-dis_merge_br_wpre_z10_p177_fasternet_nowarp_pre_la_v2.py"
  
  # 检查点路径
  ckpt: "s3://cjz-share/con-dis_model/ckpt_release/0726/z10_con-dis_model_0726_v0.pth"
  
  # 训练参数 (适合本地环境)
  max_epoch: 2  # 本地训练减少epoch数
  batch_size_per_device: 4  # 本地环境减少batch size
  total_devices: 1
  
  # 本地环境配置
  local:
    gpu_count: 1
    cpu_count: 8
    memory_gb: 16
    
  # 训练命令模板
  command_template: |
    python {script_path} \
      --config {exp} \
      --checkpoint {ckpt} \
      --max-epoch {max_epoch} \
      --batch-size {batch_size_per_device} \
      --gpus {gpu_count}

inference:
  is_hdmap: false # 是否使用高精地图
  
  # 本地推理配置
  local:
    batch_size: 1
    num_workers: 2

evaluation:
  baseline_model_path: ""  # 待配置
  metrics:
    - "accuracy"
    - "precision" 
    - "recall"
    - "f1_score"
  
  # 本地评测配置
  local:
    parallel_jobs: 2
    timeout_seconds: 3600

output:
  logs_dir: "outputs/logs"
  reports_dir: "outputs/reports"
  visualizations_dir: "outputs/visualizations"
  
  # 本地输出配置
  local:
    save_intermediate: true
    compress_outputs: false

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_rotation: "1 day"
  max_files: 7
