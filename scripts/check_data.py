#!/usr/bin/env python3
"""数据检查脚本"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import click
import json
from src.utils.logger import setup_logger, get_logger
from src.data_checker import <PERSON><PERSON><PERSON><PERSON><PERSON>, FormatValidator

logger = get_logger(__name__)


@click.command()
@click.option('--data-path', '-d', required=True, help='数据路径')
@click.option('--output', '-o', help='输出报告路径')
def main(data_path: str, output: str):
    """检查数据格式和可访问性"""
    
    setup_logger(log_file="data_check.log")
    logger.info(f"开始检查数据: {data_path}")
    
    results = {}
    
    try:
        # S3路径检查
        if data_path.startswith('s3://'):
            logger.info("检查S3路径...")
            s3_checker = S3Checker()
            s3_result = s3_checker.check_s3_path(data_path)
            results["s3_check"] = s3_result
            
            if s3_result["exists"]:
                logger.info("✓ S3路径检查通过")
            else:
                logger.error("✗ S3路径检查失败")
        
        # 格式验证
        if data_path.endswith('.json'):
            logger.info("验证JSON格式...")
            validator = FormatValidator()
            format_result = validator.validate_json_format(data_path)
            results["format_check"] = format_result
            
            if format_result["is_valid"]:
                logger.info("✓ 格式验证通过")
            else:
                logger.error("✗ 格式验证失败")
        
        # 输出结果
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"检查报告已保存到: {output}")
        else:
            print(json.dumps(results, indent=2, ensure_ascii=False, default=str))
        
        logger.info("数据检查完成")
        
    except Exception as e:
        logger.error(f"数据检查失败: {e}")
        raise


if __name__ == '__main__':
    main()
