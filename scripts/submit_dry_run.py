#!/usr/bin/env python3
"""提交RJob dry-run任务脚本"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import click
import yaml
from src.utils.logger import setup_logger, get_logger
from src.training.rjob_submitter import RJobSubmitter

logger = get_logger(__name__)


@click.command()
@click.option('--config', '-c', required=True, help='配置文件路径')
@click.option('--job-name', '-n', help='任务名称（可选）')
@click.option('--show-command', is_flag=True, help='只显示命令不执行')
def main(config: str, job_name: str, show_command: bool):
    """提交RJob dry-run任务，执行tail -f /dev/null保持容器运行"""
    
    setup_logger(log_file="dry_run.log")
    logger.info("开始提交RJob dry-run任务")
    
    # 加载配置
    with open(config, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)
    
    logger.info(f"加载配置文件: {config}")
    
    try:
        submitter = RJobSubmitter(config_data)
        
        if show_command:
            # 只显示命令
            cmd = submitter.build_rjob_command(job_name)
            logger.info("生成的RJob命令:")
            print(' '.join(cmd))
            return
        
        # 提交任务
        result = submitter.submit_dry_run_job(job_name)
        
        if result["success"]:
            logger.info(f"✓ Dry-run任务提交成功: {result['job_name']}")
            logger.info("任务已启动，容器将保持运行状态")
            logger.info(f"可以使用以下命令进入容器:")
            logger.info(f"rjob exec {result['job_name']} -- bash")
        else:
            logger.error(f"✗ Dry-run任务提交失败: {result['error']}")
            if result["output"]:
                logger.info(f"输出: {result['output']}")
        
    except Exception as e:
        logger.error(f"提交dry-run任务失败: {e}")
        raise


if __name__ == '__main__':
    main()
