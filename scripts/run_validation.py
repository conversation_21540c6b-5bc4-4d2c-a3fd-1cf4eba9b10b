#!/usr/bin/env python3
"""RJob执行训练验证流程脚本

通过RJob集群执行训练验证流程，适用于：
- 生产环境大规模训练
- 需要GPU集群资源的场景
- 长时间运行的训练任务
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import click
from src.utils.logger import setup_logger, get_logger
from src.core.pipeline import TrainingPipeline

logger = get_logger(__name__)


@click.command()
@click.option('--config', '-c',
              default='config/validation_config.yaml',
              help='配置文件路径')
@click.option('--data-path', '-d', required=True,
              help='新数据集路径')
@click.option('--dry-run', is_flag=True,
              help='只生成命令不执行')
@click.option('--step',
              type=click.Choice(['all', 'check', 'replace', 'train', 'eval', 'report']),
              default='all',
              help='执行步骤')
@click.option('--log-file',
              default='rjob_validation.log',
              help='日志文件路径')
@click.option('--verbose', '-v', is_flag=True,
              help='详细输出模式')
def main(config: str, data_path: str, dry_run: bool, step: str, log_file: str, verbose: bool):
    """通过RJob执行自动驾驶大模型自闭环验证流程

    此脚本通过RJob集群执行训练验证流程，适用于：

    \b
    - 生产环境大规模训练
    - 需要GPU集群资源的场景
    - 长时间运行的训练任务
    - 需要高性能计算资源的场景

    示例用法：

    \b
    # 执行完整流程
    python scripts/run_validation.py -d s3://path/to/data

    \b
    # 只提交训练任务
    python scripts/run_validation.py -d s3://path/to/data --step train

    \b
    # 干运行模式（只生成RJob命令）
    python scripts/run_validation.py -d s3://path/to/data --dry-run
    """

    # 设置日志
    setup_logger(log_file=log_file, verbose=verbose)
    logger.info("=" * 60)
    logger.info("开始RJob执行自动驾驶大模型自闭环验证")
    logger.info("=" * 60)

    # 检查配置文件是否存在
    if not os.path.exists(config):
        logger.error(f"配置文件不存在: {config}")
        click.echo(f"错误: 配置文件不存在: {config}", err=True)
        sys.exit(1)

    try:
        # 创建训练流程管理器 (RJob模式)
        pipeline = TrainingPipeline(config_path=config, executor_type='rjob')

        # 显示执行信息
        executor_info = pipeline.get_executor_info()
        logger.info(f"执行器类型: {executor_info['type']}")
        logger.info(f"执行器类: {executor_info['class']}")
        logger.info(f"配置文件: {executor_info['config_path']}")
        logger.info(f"数据路径: {data_path}")
        logger.info(f"执行步骤: {step}")
        logger.info(f"干运行模式: {dry_run}")

        if verbose:
            click.echo(f"✓ 使用配置文件: {config}")
            click.echo(f"✓ 数据路径: {data_path}")
            click.echo(f"✓ 执行步骤: {step}")
            click.echo(f"✓ 执行器: RJob集群模式")
            if dry_run:
                click.echo("⚠ 干运行模式 - 只生成RJob命令不实际提交")

        # 执行流程
        result = pipeline.run(
            data_path=data_path,
            dry_run=dry_run,
            step=step
        )

        # 显示结果
        if result['success']:
            logger.info("✓ 验证流程执行成功")
            if verbose:
                click.echo("\n" + "=" * 40)
                click.echo("执行结果:")
                click.echo(f"✓ 成功完成步骤: {', '.join(result['steps_completed'])}")

                # 显示RJob任务信息
                if 'training' in result and 'job_name' in result['training']:
                    job_name = result['training']['job_name']
                    click.echo(f"✓ RJob任务名称: {job_name}")
                    if not dry_run:
                        click.echo(f"✓ 可以使用以下命令查看任务状态:")
                        click.echo(f"  rjob status {job_name}")
                        click.echo(f"✓ 可以使用以下命令进入容器:")
                        click.echo(f"  rjob exec {job_name} -- bash")
        else:
            logger.error("✗ 验证流程执行失败")
            if result.get('errors'):
                for error in result['errors']:
                    logger.error(f"错误: {error}")
                    if verbose:
                        click.echo(f"✗ 错误: {error}", err=True)
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("用户中断执行")
        click.echo("\n用户中断执行")
        sys.exit(130)
    except Exception as e:
        logger.error(f"执行失败: {e}")
        click.echo(f"错误: {e}", err=True)
        if verbose:
            import traceback
            logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == '__main__':
    main()
