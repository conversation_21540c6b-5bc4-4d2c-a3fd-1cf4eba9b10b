#!/usr/bin/env python3
"""直接执行训练验证流程脚本

在本地或当前环境中直接执行训练验证流程，适用于：
- 本地开发和调试
- 小规模数据验证  
- 快速原型验证
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import click
from src.utils.logger import setup_logger, get_logger
from src.core.pipeline import TrainingPipeline

logger = get_logger(__name__)


@click.command()
@click.option('--config', '-c', 
              default='config/validation_config.yaml',
              help='配置文件路径')
@click.option('--data-path', '-d', required=True, 
              help='新数据集路径')
@click.option('--dry-run', is_flag=True, 
              help='只生成命令不执行')
@click.option('--step', 
              type=click.Choice(['all', 'check', 'replace', 'train', 'eval', 'report']), 
              default='all', 
              help='执行步骤')
@click.option('--log-file', 
              default='direct_validation.log',
              help='日志文件路径')
@click.option('--verbose', '-v', is_flag=True,
              help='详细输出模式')
def main(config: str, data_path: str, dry_run: bool, step: str, log_file: str, verbose: bool):
    """直接执行自动驾驶大模型自闭环验证流程
    
    此脚本在本地或当前环境中直接执行训练验证流程，适用于：
    
    \b
    - 本地开发和调试
    - 小规模数据验证
    - 快速原型验证
    - 不需要集群资源的场景
    
    示例用法：
    
    \b
    # 执行完整流程
    python scripts/direct_run.py -d /path/to/data
    
    \b
    # 只执行数据检查
    python scripts/direct_run.py -d /path/to/data --step check
    
    \b
    # 干运行模式
    python scripts/direct_run.py -d /path/to/data --dry-run
    """
    
    # 设置日志
    setup_logger(log_file=log_file, verbose=verbose)
    logger.info("=" * 60)
    logger.info("开始直接执行自动驾驶大模型自闭环验证")
    logger.info("=" * 60)
    
    # 检查配置文件是否存在
    if not os.path.exists(config):
        logger.error(f"配置文件不存在: {config}")
        click.echo(f"错误: 配置文件不存在: {config}", err=True)
        sys.exit(1)
    
    try:
        # 创建训练流程管理器
        pipeline = TrainingPipeline(config_path=config, executor_type='direct')
        
        # 显示执行信息
        executor_info = pipeline.get_executor_info()
        logger.info(f"执行器类型: {executor_info['type']}")
        logger.info(f"执行器类: {executor_info['class']}")
        logger.info(f"配置文件: {executor_info['config_path']}")
        logger.info(f"数据路径: {data_path}")
        logger.info(f"执行步骤: {step}")
        logger.info(f"干运行模式: {dry_run}")
        
        if verbose:
            click.echo(f"✓ 使用配置文件: {config}")
            click.echo(f"✓ 数据路径: {data_path}")
            click.echo(f"✓ 执行步骤: {step}")
            click.echo(f"✓ 执行器: 直接执行模式")
            if dry_run:
                click.echo("⚠ 干运行模式 - 不会实际执行训练")
        
        # 执行流程
        result = pipeline.run(
            data_path=data_path,
            dry_run=dry_run,
            step=step
        )
        
        # 显示结果
        if result['success']:
            logger.info("✓ 验证流程执行成功")
            if verbose:
                click.echo("\n" + "=" * 40)
                click.echo("执行结果:")
                click.echo(f"✓ 成功完成步骤: {', '.join(result['steps_completed'])}")
                
                # 显示各步骤的详细结果
                for step_name in result['steps_completed']:
                    if step_name in result:
                        step_result = result[step_name]
                        if isinstance(step_result, dict) and 'message' in step_result:
                            click.echo(f"  - {step_name}: {step_result['message']}")
        else:
            logger.error("✗ 验证流程执行失败")
            if result.get('errors'):
                for error in result['errors']:
                    logger.error(f"错误: {error}")
                    if verbose:
                        click.echo(f"✗ 错误: {error}", err=True)
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("用户中断执行")
        click.echo("\n用户中断执行")
        sys.exit(130)
    except Exception as e:
        logger.error(f"执行失败: {e}")
        click.echo(f"错误: {e}", err=True)
        if verbose:
            import traceback
            logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        sys.exit(1)


@click.command()
@click.option('--config', '-c', 
              default='config/validation_config.yaml',
              help='配置文件路径')
@click.option('--data-path', '-d', required=True,
              help='数据路径')
def check_only(config: str, data_path: str):
    """只执行数据检查步骤"""
    setup_logger(log_file='data_check.log')
    
    try:
        pipeline = TrainingPipeline(config_path=config, executor_type='direct')
        result = pipeline.run_step('check', data_path=data_path)
        
        if result['success']:
            click.echo("✓ 数据检查通过")
        else:
            click.echo("✗ 数据检查失败", err=True)
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"错误: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    main()
